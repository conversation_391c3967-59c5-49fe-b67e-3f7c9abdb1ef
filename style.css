/* 全局平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 顶部导航栏 */
.top-nav {
    background-color: #0a3159;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s, padding 0.3s;
}

.nav-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: padding 0.3s;
}

.nav-title a {
    color: #fff;
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
    transition: font-size 0.3s;
    white-space: nowrap;
    /* 移除移动端点击时的白色背景高亮效果 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.nav-title a:active {
    background-color: transparent !important;
}
.nav-title a:focus {
    outline: none;
    background-color: transparent !important;
}

.nav-links {
    display: flex;
    gap: 20px;
    flex-wrap: nowrap;
}

/* 导航链接样式 */
.nav-links a {
    color: #fff;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 3px;
    white-space: nowrap;
    /* 移除移动端点击时的白色背景高亮效果 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.nav-links a:hover {
    text-decoration: none;
}
.nav-links a:active {
    background-color: transparent !important;
}
.nav-links a:focus {
    outline: none;
    background-color: transparent !important;
}

/* 移动端导航栏样式 */
@media (max-width: 768px) {
    .top-nav {
        background-color: #0a3159;
    }
    
    .nav-container {
        flex-direction: row;
        padding: 8px 15px;
        width: 100%;
    }
    
    .nav-title {
        font-size: 0.9rem;
        flex-shrink: 0;
        margin-right: 15px;
    }
    
    .nav-title a {
        font-size: 1.2rem;
    }
    
    .nav-links {
        margin-top: 0;
        gap: 0;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }
    
    .nav-links::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }
    
    .nav-links a {
        font-size: 0.9rem;
        padding: 5px 10px;
        flex-shrink: 0;
        border-radius: 0;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 8px 10px;
    }
    
    .nav-title a {
        font-size: 1.1rem;
    }
    
    .nav-links {
        gap: 0;
    }
    
    .nav-links a {
        font-size: 0.8rem;
        padding: 5px 8px;
    }
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Times New Roman', Times, serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
    font-size: 16px;
    
}

body::after{
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(to right, rgba(240, 240, 235, 0.4) 1px, transparent 1px),
                      linear-gradient(to bottom, rgba(240, 240, 235, 0.4) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: -1;
    pointer-events: none;
}

/* 调整container顶部内边距，为固定导航栏腾出空间 */
.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 80px 20px 20px;
}

/* Typography */
h1 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    color: #000;
}

h2 {
    font-size: 1.8rem;
    font-weight: bold;
    color: #000;
    padding-bottom: 10px;
}

h3 {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 8px;
    color: #000;
}

p {
    margin-bottom: 10px;
    text-align: justify;
}

a {
    color: #0066cc;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

strong {
    font-weight: bold;
}

/* Header Section */
.main-header {
    margin-bottom: 40px;
}

.header-content {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    margin-bottom: 20px;
}

.profile-photo {
    flex: 0 0 230px;
}

.profile-photo img {
    width: 230px;
    height: 250px;
    object-fit: cover;
    border-radius: 0;
}

.profile-content {
    flex: 1;
}

.profile-content h1 {
    font-size: 2.5rem;
    margin-bottom: 5px;
    color: #000;
}

.title {
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: #333;
}

.affiliation {
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: #333;
    line-height: 1.4;
}

.email {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: #333;
}

.social-links {
    font-size: 1.1rem;
    margin-bottom: 0;
}

.social-links a {
    color: #0066cc;
    text-decoration: none;
    margin-right: 3px;
}

.bio-content {
    margin-top: 0;
    max-width: 1000px;
    width: 100%;
    margin: 0 auto;
}

.bio {
    margin-bottom: 15px;
    font-size: 1.1rem;
    text-align: justify;
    line-height: 1.5;
}

.looking {
    color: #cc0000;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.student-note {
    margin-bottom: 15px;
    font-size: 1.1rem;
}

/* News Section */
.news-section {
    margin-bottom: 30px;
}

.news-list {
    margin-left: 20px;
}

.news-item {
    margin-bottom: 10px;
    display: flex;
    gap: 10px;
}

.news-date {
    color: #0066cc;
    font-weight: bold;
    min-width: 100px;
}

.news-content {
    font-size: 1.1rem;
    flex: 1;
}

/* Publications Section */
.publications-section {
    margin-bottom: 30px;
}

.publications-list {
    margin-left: 20px;
}

.publication-item {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eaeaea;
    align-items: center;
}

.publication-item:last-child {
    border-bottom: none;
}

.pub-image {
    flex: 0 0 284px;
    position: relative;
    width: 284px;
    height: 156px;
}

.pub-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    display: block;
}

.pub-badge {
    position: absolute;
    top: 0;
    left: 0;
    background-color: #0066cc;
    color: white;
    padding: 4px 8px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 2;
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.pub-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.pub-content h3 {
    margin-bottom: 5px;
    line-height: 1.3;
}

.authors {
    margin-bottom: 3px;
    font-style: italic;
}

.venue {
    margin-bottom: 8px;
    font-weight: bold;
}

.pub-links {
    margin-bottom: 8px;
}

.pub-links a {
    margin-right: 10px;
    color: #0066cc;
}

.pub-links .link-disabled {
    color: #999;
    margin-right: 10px;
}

.pub-description {
    font-style: italic;
    color: #666;
}

/* Research Projects Section */
.research-projects-section {
    margin-bottom: 30px;
}

.research-projects-section .projects-list {
    margin-left: 20px;
}

.projects-section {
    margin-bottom: 30px;
}

.projects-list {
    margin-left: 20px;
}

.project-item {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eaeaea;
    align-items: center;
}

.project-item:last-child {
    border-bottom: none;
}

.project-image {
    flex: 0 0 284px;
    position: relative;
    width: 284px;
    height: 156px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    display: block;
}

.project-badge {
    position: absolute;
    top: 0;
    left: 0;
    background-color: #0066cc;
    color: white;
    padding: 4px 8px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 2;
    max-width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.project-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.project-content h3 {
    margin-bottom: 5px;
}

.project-content p {
    color: #666;
    font-size: 16px;
    font-style: italic;
}

.project-links {
    margin-bottom: 8px;
}

.project-links a {
    margin-right: 10px;
    color: #0066cc;
}

.project-links .link-disabled {
    color: #999;
    margin-right: 10px;
}

/* Education Section */
.education-section {
    margin-bottom: 30px;
}

.education-list {
    margin-left: 20px;
}

.education-item {
    display: flex;
    gap: 5px;
    margin-bottom: 8px;
    padding-bottom: 5px;
}

.edu-period {
    flex: 0 0 100px;
    font-weight: bold;
    color: #0066cc;
    font-size: 0.9rem;
}

.edu-content {
    flex: 1;
}

.edu-content h3 {
    margin-bottom: 6px;
    font-size: 1.2rem;
    line-height: 1.3;
}

.edu-content p {
    margin-bottom: 6px;
    color: #666;
    font-size: 1rem;
}

/* Footer */
.main-footer {
    margin-top: 40px;
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.visitor-counter {
    display: inline-block;
    margin-bottom: 20px;
}

.footer-links {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 10px;
}

.footer-follow {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    color: #666;
    font-size: 0.9rem;
}

.footer-links a {
    color: #666;
    font-size: 0.9rem;
    text-decoration: none;
}

.footer-links a:hover {
    text-decoration: underline;
    color: #333;
}

.footer-info {
    margin-top: 10px;
}

.footer-info p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
    text-align: center;
    line-height: 1.5;
}

/* Citation Modal */
.citation-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.citation-modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 15px;
    border: 1px solid #ddd;
    width: 80%;
    max-width: 700px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: relative;
}

.citation-close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: #666;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.citation-close:hover {
    color: #000;
}

.citation-text-container {
    background-color: #f8f8f8;
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
    border: 1px solid #e0e0e0;
    max-height: 300px;
    overflow-y: auto;
}

#citation-text {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
    line-height: 1.5;
}

.copy-citation-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
}

.copy-citation-btn:active {
    background-color: #3e8e41;
}

.copy-citation-link {
    display: inline-block;
    color: #0066cc;
    text-decoration: underline;
    font-size: 16px;
    cursor: pointer;
}

.copy-citation-link:hover {
    color: #004499;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        max-width: 90%;
        padding: 20px;
    }
    
    .header-content {
        gap: 20px;
    }
    
    .profile-photo {
        flex: 0 0 200px;
    }
    
    .profile-photo img {
        width: 200px;
        height: 200px;
    }
    
    .pub-image {
        flex: 0 0 240px;
    }
    
    .pub-image img {
        width: 240px;
        height: 132px;
    }
    
    .project-image {
        flex: 0 0 240px;
    }
    
    .project-image img {
        width: 240px;
        height: 132px;
    }
}

@media (max-width: 768px) {
    .top-nav, .top-nav.scrolled {
        background-color: #0a3159 !important;
    }
    
    .container {
        padding: 60px 15px 20px;
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .profile-photo {
        margin: 0;
        width: 230px;
    }
    
    .profile-photo img {
        width: 230px;
        height: 250px;
        margin: 0;
    }
    
    .profile-content {
        text-align: left;
        margin-top: 10px;
        padding-left: 0;
    }
    
    .title, .affiliation, .email {
        text-align: left;
    }
    
    .social-links {
        text-align: left;
        margin-bottom: 15px;
    }
    
    .bio-content {
        margin-top: 20px;
        padding-left: 0;
    }
    
    .bio, .looking, .student-note {
        text-align: justify;
        font-size: 1.1rem;
        line-height: 1.5;
        margin-bottom: 10px;
        padding-left: 0;
    }

    .research-interests {
        text-align: justify;
        font-size: 1.1rem;
        line-height: 1.5;
        margin-bottom: 10px;
    }
    
    .contact-links {
        font-size: 1rem;
        line-height: 1.5;
        text-align: center;
        margin-bottom: 15px;
        order: 3;
    }
    
    .contact-links a {
        margin-right: 8px;
    }
    
    .publication-item,
    .project-item {
        flex-direction: column;
        gap: 10px;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eaeaea;
        align-items: center;
    }
    
    .publication-item:last-child,
    .project-item:last-child {
        border-bottom: none;
    }
    
    .research-projects-section .projects-list {
        margin-left: 20px;
    }
    
    .pub-image,
    .project-image {
        align-self: center;
        flex: none;
        position: relative;
        margin-bottom: 10px;
        width: 100%;
        max-width: 260px;
        height: auto;
        aspect-ratio: 284/156;
    }
    
    .pub-image img,
    .project-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    }
    
    .pub-badge,
    .project-badge {
        padding: 3px 6px;
        font-size: 0.75rem;
    }
    
    .education-item {
        flex-direction: column;
        gap: 5px;
        margin-bottom: 15px;
        padding-bottom: 8px;
    }
    
    .edu-period {
        flex: none;
        font-size: 1rem;
        color: #0066cc;
        font-weight: bold;
        margin-bottom: 0;
        order: 1;
    }
    
    .edu-content {
        order: 2;
    }
    
    .edu-content h3 {
        margin-top: 0;
        margin-bottom: 1px;
        font-size: 19.2px;
        line-height: 1.2;
    }
    
    .edu-content p {
        margin-bottom: 1px;
        font-size: 19.2px;
    }
    
    h1 {
        font-size: 2rem;
        margin-bottom: 10px;
        line-height: 1.2;
    }
    
    h2 {
        font-size: 1.6rem;
        margin: 20px 0 8px 0;
        line-height: 1.2;
    }
    
    h3 {
        font-size: 1.2rem;
        line-height: 1.3;
    }
    
    .news-item {
        flex-direction: column;
        gap: 2px;
        margin-bottom: 10px;
    }
    
    .news-date {
        min-width: auto;
        color: #0066cc;
        font-size: 0.9rem;
        font-weight: bold;
    }
    
    .news-content {
        font-size: 1rem;
        line-height: 1.3;
    }
    
    .pub-links a,
    .project-links a {
        margin-right: 8px;
        font-size: 1rem;
    }
    
    .citation-modal-content {
        margin: 15% auto;
        width: 90%;
        padding: 20px;
    }
    
    .citation-text-container {
        max-height: 250px;
        padding: 12px;
    }
    
    #citation-text {
        font-size: 13px;
    }
    
    .copy-citation-link {
        font-size: 15px;
    }
    
    .visitor-counter {
        transform: scale(0.8);
    }
    
    .footer-links {
        margin: 10px 0;
    }
    
    .footer-follow {
        font-size: 0.85rem;
        gap: 8px;
    }
    
    /* 移动端学校图标调整 */
    .bio img[src*="icon-title"] {
        width: 18px !important;
        height: 18px !important;
        margin-left: 3px !important;
    }
    
    .bio img[src*="scuec.png"] {
        width: 65px !important;
        height: 25px !important;
        margin-left: 3px !important;
    }
    
    .contact-links a {
        padding: 0 !important;
        margin: 0 8px 0 0 !important;
        background-color: transparent !important;
        border-radius: 0 !important;
    }
    
    .contact-links a:hover {
        background-color: transparent !important;
    }
    
    .pub-links a,
    .project-links a {
        padding: 0 !important;
        margin: 0 8px 0 0 !important;
        background-color: transparent !important;
        border-radius: 0 !important;
    }
    
    .pub-links a:hover,
    .project-links a:hover {
        background-color: transparent !important;
    }
    
    
    .news-item {
        background-color: transparent !important;
        border-radius: 0 !important;
        padding: 0 !important;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 50px 10px 15px;
    }
    
    body {
        font-size: 16px;
    }
    
    h1 {
        font-size: 1.8rem;
        line-height: 1.1;
        margin-bottom: 8px;
        text-align: left;
    }
    
    h2 {
        font-size: 1.4rem;
        margin: 15px 0 6px 0;
        line-height: 1.1;
    }
    
    h3 {
        font-size: 1.1rem;
        line-height: 1.2;
    }
    
    .profile-photo {
        width: 230px;
    }
    
    .profile-photo img {
        width: 230px;
        height: 250px;
    }
    
    .profile-content h1 {
        font-size: 1.8rem;
        margin-bottom: 5px;
    }
    
    .title, .affiliation, .email {
        font-size: 1rem;
    }
    
    .social-links {
        font-size: 0.9rem;
    }
    
    .bio, .looking, .student-note {
        font-size: 1rem;
        line-height: 1.4;
        text-align: justify;
    }
    
    .contact-links {
        text-align: center;
        margin-bottom: 15px;
    }
    
    .bio, .research-interests {
        font-size: 1rem;
        line-height: 1.4;
        margin-bottom: 8px;
        text-align: justify;
    }
    
    .contact-links {
        font-size: 0.9rem;
    }
    
    .contact-links a {
        margin-right: 6px;
    }
    
    .news-list,
    .publications-list,
    .research-projects-section .projects-list,
    .projects-list,
    .education-list {
        margin-left: 5px;
    }
    
    .news-item {
        margin-bottom: 8px;
    }
    
    .news-date {
        font-size: 0.85rem;
        margin-bottom: 1px;
    }
    
    .news-content {
        font-size: 0.9rem;
        line-height: 1.2;
    }
    
    .pub-image,
    .project-image {
        max-width: 100%;
        width: 100%;
        height: auto;
        aspect-ratio: 16/9;
    }
    
    .pub-image img,
    .project-image img {
        width: 100%;
        height: 100%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    }
    
    .pub-badge,
    .project-badge {
        padding: 2px 5px;
        font-size: 0.8rem;
    }
    
    .pub-content,
    .project-content {
        padding: 0;
        text-align: left;
        display: block;
    }
    
    .authors {
        font-size: 0.9rem;
        margin-bottom: 2px;
    }
    
    .venue {
        font-size: 0.9rem;
        margin-bottom: 4px;
    }
    
    .pub-description,
    .project-content p {
        font-size: 0.85rem;
        line-height: 1.2;
    }
    
    .pub-links a,
    .project-links a {
        margin-right: 6px;
        font-size: 0.85rem;
    }
    
    .education-item {
        flex-direction: column;
        gap: 3px;
        margin-bottom: 12px;
        padding-bottom: 2px;
    }
    
    .edu-period {
        flex: none;
        font-size: 0.9rem;
        order: 1;
        margin-bottom: 2px;
    }
    
    .edu-content {
        order: 2;
    }
    
    .edu-content h3 {
        font-size: 1rem;
    }
    
    .edu-content p {
        font-size: 0.85rem;
    }
    
    .visitor-counter {
        transform: scale(0.7);
        margin-bottom: 5px;
    }
    
    .footer-links a,
    .footer-follow,
    .footer-info p {
        font-size: 0.8rem;
    }
    
    /* 移动端学校图标调整 */
    .bio img[src*="icon-title"] {
        width: 15px !important;
        height: 15px !important;
        margin-left: 2px !important;
    }
    
    .bio img[src*="scuec.png"] {
        width: 60px !important;
        height: 20px !important;
        margin-left: 2px !important;
    }
    
    .citation-modal-content {
        margin: 20% auto;
        width: 95%;
        padding: 15px;
    }
    
    .citation-text-container {
        max-height: 200px;
        padding: 10px;
    }
    
    #citation-text {
        font-size: 12px;
    }
    
    .citation-close {
        top: 10px;
        right: 15px;
        font-size: 24px;
    }
    
    .copy-citation-link {
        font-size: 14px;
    }
}

/* Print Styles */
@media print {
    .visitor-counter {
        display: none;
    }
    
    .citation-modal {
        display: none;
    }
    
    a {
        color: #000;
        text-decoration: underline;
    }
}

/* 滚动和交互体验优化 */
@media (max-width: 768px) {
    html {
        scroll-behavior: smooth;
    }
    
    body {
        -webkit-overflow-scrolling: touch;
    }
    
    /* 优化文本选择 */
    p, h1, h2, h3 {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
    }
    
    /* 防止文本缩放 */
    input, textarea, select {
        font-size: 16px;
    }
    
    /* Visitor Counter 溢出处理 */
    .visitor-counter {
        overflow-x: auto;
        overflow-y: hidden;
        width: 100%;
        display: flex;
        justify-content: center;
    }
    
    .visitor-counter > * {
        max-width: 100%;
    }
} 

/* 确保导航栏在所有情况下都保持深蓝色背景 */
.top-nav, .top-nav.scrolled {
    background-color: #0a3159 !important;
} 

/* 会议或期刊等级样式 */
.venue-rank {
    display: inline-block;
    color: red;
    font-weight: bold;
    /* 斜体 */
    font-style: italic;
    margin: 0 5px;
} 

/* 回到顶部按钮样式 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: white;
    color: #0a3159;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1000;
    font-size: 20px;
    /* 移除移动端点击时的背景高亮效果 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: #f5f5f5;
}

.back-to-top:active {
    background-color: white !important;
    transform: scale(0.95);
}

.back-to-top:focus {
    outline: none;
    background-color: white !important;
}

/* 个人资料内容样式更新 */
.profile-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;
    width: 100%;
}

.profile-name {
    font-size: 28px;
    font-weight: bold;
    margin: 10px 0;
    color: #003366;
    text-align: center;
    width: 100%;
}

.profile-title {
    font-size: 18px;
    margin: 5px 0;
    color: #555;
    text-align: center;
    width: 100%;
}

.profile-affiliation {
    font-size: 16px;
    margin: 5px 0;
    color: #666;
    text-align: center;
    width: 100%;
}

.profile-location {
    font-size: 16px;
    margin: 5px 0;
    color: #666;
    text-align: center;
    width: 100%;
}

.profile-email {
    font-size: 16px;
    margin: 5px 0;
    color: #666;
    text-align: center;
    width: 100%;
}

.social-icons {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 25px;
}

.social-icons a {
    font-size: 28px;
    color: #003366;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.social-icons a:hover {
    text-decoration: none;
    color: #0066cc;
    transform: translateY(-3px);
} 

/* 圆形头像样式 */
.rounded-profile-img {
    width: 180px;
    height: 180px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
} 

/* 个人资料容器样式 */
.profile-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
    text-align: center;
}

.header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-top: 20px;
    text-align: center;
} 

/* 主标题样式 */
.main-header {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
} 